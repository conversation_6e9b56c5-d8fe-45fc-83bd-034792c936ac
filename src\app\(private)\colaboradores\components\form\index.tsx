'use client'

import { <PERSON><PERSON>, use<PERSON><PERSON>t } from '@ads/components-react'
import { zodResolver } from '@hookform/resolvers/zod'
import { User } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import { useMutation } from 'react-query'

import { CollaboratorsFormSkeleton } from '@/components/loaders/skeletons/collaboratorsForm'
import { InputField } from '@/components/ui/input-form'
import { SelectAds } from '@/components/ui/select-ads'
import { Separator } from '@/components/ui/separator'
import {
  allPositions,
  allSeniorities,
  collaboratorTypes,
} from '@/consts/collaborator'
import { useAuth } from '@/contexts/AuthContext'
import { EPosition } from '@/enum/position'
import { ESeniority } from '@/enum/seniority'
import { GetGroupsQuery, GetTeamsQuery } from '@/graphql/generated/graphql'
import { createUser } from '@/http/collaborators/create-collaborator'
import { editCollaborator } from '@/http/collaborators/update-collaborator'
import { Avatar } from '@/model/avatar'
import { IUser } from '@/model/user'

import { AvatarSelection } from '../avatar'
import { ProfileFormData, profileSchema } from './validations'

type CollaboratorFormProps = {
  isEditMode?: boolean
  defaultValues?: IUser
  isLoadingRequests?: boolean
  allTeams?: GetTeamsQuery
  allGroups?: GetGroupsQuery
}

export function CollaboratorForm({
  isEditMode,
  defaultValues,
  isLoadingRequests,
  allTeams,
  allGroups,
}: CollaboratorFormProps) {
  const router = useRouter()
  const { user } = useAuth()
  const { alert } = useAlert()

  const [avatarSelected, setAvatarSelected] = useState<Avatar | null>(null)

  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isSubmitting },
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: defaultValues && {
      name: defaultValues?.name,
      email: defaultValues?.email,
      collaboratorType:
        defaultValues?.roles?.[0]?.name.toLowerCase() === 'b2b_student'
          ? 'student'
          : 'admin',
      team: String(defaultValues?.metadata?.squad_id),
      position: defaultValues?.position || '',
      seniority: defaultValues?.seniority || '',
      birthdate: defaultValues?.birthdate,
      admitedAt: defaultValues?.admitted_at,
    },
  })

  const {
    mutate: createCollaboratorMutation,
    isLoading: isCreatingCollaborator,
  } = useMutation({
    mutationFn: (data: ProfileFormData) =>
      createUser({
        data: {
          name: data.name,
          email: data.email,
          role_ids: [data.collaboratorType === 'student' ? 4 : 5],
          company_id: user?.metadata?.company_id as number,
          redirect_url:
            data.collaboratorType === 'student'
              ? String(process.env.NEXT_PUBLIC_REDIRECT_URL_LMS)
              : String(process.env.NEXT_PUBLIC_REDIRECT_URL_B2B),
          enrollment_id: 1,
          squad_id: Number(data.team),
          avatar: avatarSelected ?? undefined,
          groups_ids: data.group ? [Number(data.group)] : [],
          position: data.position as EPosition,
          seniority: data.seniority as ESeniority,
          birthdate: data.birthdate,
          admitted_at: data.admitedAt,
        },
      }),
    onSuccess: () => {
      alert({
        title: 'Colaborador criado com sucesso',
        description: 'O colaborador foi criado com sucesso.',
        alertType: 'success',
      })
      router.push('/colaboradores')
    },
    onError: () => {
      alert({
        title: 'Erro ao criar colaborador',
        description: 'Ocorreu um erro ao criar o colaborador. Tente novamente.',
        alertType: 'danger',
      })
    },
  })

  const {
    mutate: updateCollaboratorMutation,
    isLoading: isUpdatingCollaborator,
  } = useMutation({
    mutationFn: (data: ProfileFormData) =>
      editCollaborator({
        userId: Number(defaultValues?.id),
        data: {
          name: data.name,
          email: data.email,
          role_ids: [data.collaboratorType === 'student' ? 4 : 5],
          company_id: user?.metadata?.company_id as number,
          redirect_url:
            data.collaboratorType === 'student'
              ? String(process.env.NEXT_PUBLIC_REDIRECT_URL_LMS)
              : String(process.env.NEXT_PUBLIC_REDIRECT_URL_B2B),
          squad_id: Number(data.team),
          avatar: avatarSelected ?? undefined,
          groups_ids: data.group ? [Number(data.group)] : [],
          position: data.position as EPosition,
          seniority: data.seniority as ESeniority,
          birthdate: data.birthdate,
          admitted_at: data.admitedAt,
        },
      }),
    onSuccess: () => {
      alert({
        title: 'Colaborador atualizado com sucesso',
        description: 'O colaborador foi atualizado com sucesso.',
        alertType: 'success',
      })
      router.push('/colaboradores')
    },
    onError: () => {
      alert({
        title: 'Erro ao atualizar colaborador',
        description:
          'Ocorreu um erro ao atualizar o colaborador. Tente novamente.',
        alertType: 'danger',
      })
    },
  })

  const onSubmit = async (data: ProfileFormData) => {
    if (isEditMode) {
      return updateCollaboratorMutation(data)
    }
    createCollaboratorMutation(data)
  }

  useEffect(() => {
    if (defaultValues?.avatar) {
      setAvatarSelected(defaultValues.avatar)
    }
  }, [defaultValues])

  const handleCancel = () => {
    router.push('/colaboradores')
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div className="space-y-8 rounded-md bg-ctx-layout-body px-6 py-10">
        {isLoadingRequests ? (
          <CollaboratorsFormSkeleton />
        ) : (
          <>
            <AvatarSelection
              avatarSelected={avatarSelected}
              setAvatarSelected={setAvatarSelected}
            />

            <section className="space-y-9">
              <h2 className="flex gap-2 text-ctx-content-title ts-heading-sm">
                <User /> Dados do colaborador
              </h2>

              <div className="grid grid-cols-2 gap-8">
                <InputField
                  id="nome"
                  label="Nome"
                  type="text"
                  placeholder="Digite o nome completo"
                  errorMessage={errors.name?.message}
                  {...register('name')}
                />

                <InputField
                  id="email"
                  label="Email"
                  type="email"
                  placeholder="Digite o email"
                  errorMessage={errors.email?.message}
                  {...register('email')}
                />

                <Controller
                  name="team"
                  control={control}
                  render={({ field }) => (
                    <SelectAds
                      label="Equipe (Opcional)"
                      placeholder="Selecione uma equipe"
                      disabled={
                        isLoadingRequests ||
                        allTeams?.companySquads.data.length === 0
                      }
                      options={
                        allTeams?.companySquads.data.map(({ id, title }) => ({
                          value: String(id),
                          label: title,
                        })) || []
                      }
                      onValueChange={field.onChange}
                      value={field.value}
                    />
                  )}
                />

                <Controller
                  name="group"
                  control={control}
                  render={({ field }) => (
                    <SelectAds
                      label="Grupo (Opcional)"
                      placeholder="Selecione um grupo"
                      disabled={
                        isLoadingRequests ||
                        allGroups?.companyGroups.data.length === 0
                      }
                      options={
                        allGroups?.companyGroups.data.map(({ id, name }) => ({
                          value: String(id),
                          label: name,
                        })) || []
                      }
                      onValueChange={field.onChange}
                      value={field.value || ''}
                    />
                  )}
                />

                <Controller
                  name="collaboratorType"
                  control={control}
                  render={({ field }) => (
                    <SelectAds
                      label="Tipo de colaborador"
                      placeholder="Selecione o tipo"
                      options={collaboratorTypes}
                      onValueChange={field.onChange}
                      value={field.value}
                      errorMessage={errors.collaboratorType?.message}
                    />
                  )}
                />
              </div>

              <Separator />

              <div className="grid grid-cols-2 gap-8">
                <Controller
                  name="position"
                  control={control}
                  render={({ field }) => (
                    <SelectAds
                      label="Cargo (Opcional)"
                      placeholder="Selecione um cargo"
                      options={allPositions}
                      onValueChange={field.onChange}
                      errorMessage={errors.position?.message}
                      value={field.value || ''}
                    />
                  )}
                />

                <Controller
                  name="seniority"
                  control={control}
                  render={({ field }) => (
                    <SelectAds
                      label="Nível (Opcional)"
                      placeholder="Selecione um nível"
                      options={allSeniorities}
                      onValueChange={field.onChange}
                      errorMessage={errors.seniority?.message}
                      value={field.value || ''}
                    />
                  )}
                />

                <Controller
                  name="birthdate"
                  control={control}
                  render={({ field }) => (
                    <InputField
                      type="date"
                      label="Data de Nascimento (Opcional)"
                      placeholder="Selecione a data de nascimento"
                      value={field.value as string}
                      onChange={(date) => field.onChange(date)}
                      errorMessage={errors.birthdate?.message}
                    />
                  )}
                />

                <Controller
                  name="admitedAt"
                  control={control}
                  render={({ field }) => (
                    <InputField
                      type="date"
                      label="Data de Admissão (Opcional)"
                      placeholder="Selecione a data de admissão"
                      value={field.value as string}
                      onChange={(date) => field.onChange(date)}
                      errorMessage={errors.admitedAt?.message}
                    />
                  )}
                />
              </div>

              <div className="flex justify-end gap-4">
                <Button
                  type="button"
                  hierarchy="tertiary"
                  onClick={handleCancel}
                  disabled={
                    isSubmitting ||
                    isCreatingCollaborator ||
                    isUpdatingCollaborator
                  }
                >
                  Cancelar
                </Button>
                <Button
                  type="submit"
                  isLoading={isCreatingCollaborator || isUpdatingCollaborator}
                  disabled={isSubmitting}
                >
                  Salvar Alterações
                </Button>
              </div>
            </section>
          </>
        )}
      </div>
    </form>
  )
}
