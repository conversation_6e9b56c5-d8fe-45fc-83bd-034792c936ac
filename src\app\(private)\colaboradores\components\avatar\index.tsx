'use client'

import { <PERSON><PERSON>, But<PERSON>, useAlert } from '@ads/components-react'
import { useRef } from 'react'
import { HiOutlinePencil } from 'react-icons/hi2'
import { IoTrashOutline } from 'react-icons/io5'
import { RxAvatar } from 'react-icons/rx'
import { useMutation } from 'react-query'

import { uploadFile, UploadFileResponse } from '@/http/files'
import { Avatar as AvatarType } from '@/model/avatar'

type AvatarSelectionProps = {
  avatarSelected: AvatarType | null
  setAvatarSelected: (avatar: AvatarType | null) => void
}

export function AvatarSelection({
  avatarSelected,
  setAvatarSelected,
}: AvatarSelectionProps) {
  const { alert } = useAlert()
  const fileInputRef = useRef<HTMLInputElement>(null)

  const { mutate, isLoading: isUploading } = useMutation(
    async ({ files, fileTypes }: { files: File[]; fileTypes: string }) => {
      const result = await uploadFile(files, fileTypes)
      if (!result) {
        throw new Error('Upload failed')
      }
      return result
    },
    {
      onSuccess: (data: UploadFileResponse) => {
        setAvatarSelected(data as AvatarType)
        alert({
          title: 'Imagem enviada com sucesso',
          description: 'A imagem foi enviada com sucesso.',
          alertType: 'success',
        })
      },
      onError: () => {
        alert({
          title: 'Erro ao enviar imagem',
          description: 'Ocorreu um erro ao enviar a imagem. Tente novamente.',
          alertType: 'danger',
        })
      },
    }
  )

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return

    const file = files[0]

    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      alert({
        title: 'Tipo de arquivo inválido',
        description: 'Por favor, selecione uma imagem (JPEG, PNG ou WebP).',
        alertType: 'danger',
      })
      return
    }

    // Validar tamanho do arquivo (2MB)
    const maxSize = 2 * 1024 * 1024 // 2MB em bytes
    if (file.size > maxSize) {
      alert({
        title: 'Arquivo muito grande',
        description: 'O arquivo deve ter no máximo 2MB.',
        alertType: 'danger',
      })
      return
    }

    const filesArray = [file]

    mutate({
      files: filesArray,
      fileTypes: 'image',
    })
  }

  const handleUploadClick = () => {
    fileInputRef.current?.click()
  }

  const handleDeleteAvatar = () => {
    setAvatarSelected(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  return (
    <section className="flex items-center justify-between gap-4 rounded-md bg-ctx-layout-surface p-4">
      {/* Input de arquivo oculto */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/jpg,image/png,image/webp"
        onChange={handleFileSelect}
        style={{ display: 'none' }}
      />

      <div className="flex items-center justify-center gap-4">
        <div className="flex h-20 w-20 items-center justify-center rounded-full bg-ctx-interactive-secondaryHover">
          {avatarSelected?.url ? (
            <Avatar src={avatarSelected.url} size="xl" />
          ) : (
            <RxAvatar size={36} />
          )}
        </div>
        <div className="flex flex-col gap-1">
          <span className="text-ctx-content-title ts-heading-xxs">
            Foto de perfil
          </span>
          <span className="text-ctx-content-base ts-paragraph-xxxs">
            Tamanho recomendado da foto: 120 x 120 px
          </span>
          <span className="text-ctx-content-base ts-paragraph-xxxs">
            Tamanho do arquivo não superior a 2 MB
          </span>
        </div>
      </div>
      <div className="flex flex-col gap-2">
        <Button
          hierarchy="secondary"
          trailingIcon={HiOutlinePencil}
          onClick={handleUploadClick}
          isLoading={isUploading}
          disabled={isUploading}
        >
          {isUploading ? 'Enviando...' : 'Alterar Foto'}
        </Button>
        <Button
          hierarchy="tertiary"
          trailingIcon={IoTrashOutline}
          onClick={handleDeleteAvatar}
          disabled={!avatarSelected || isUploading}
        >
          Excluir Foto
        </Button>
      </div>
    </section>
  )
}
