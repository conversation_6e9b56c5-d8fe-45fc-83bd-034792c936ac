'use client'

import { useQuery } from 'react-query'

import { PageLayout } from '@/components/layouts/page-layout'
import { useAuth } from '@/contexts/AuthContext'
import { getGroups } from '@/http/groups'
import { getTeams } from '@/http/teams/get-teams'

import { CollaboratorForm } from '../components/form'

export default function NovoColaborador() {
  const { user } = useAuth()

  const { data: allTeams, isLoading: isLoadingTeams } = useQuery({
    queryKey: ['getTeams', user],
    queryFn: () =>
      getTeams({
        limit: 999,
        page: 1,
        company_id: user?.metadata?.company_id as number,
      }),
    refetchOnWindowFocus: false,
  })

  const { data: allGroups, isLoading: isLoadingGroups } = useQuery({
    queryKey: ['getGroups', user],
    queryFn: () =>
      getGroups({
        limit: 999,
        page: 1,
        company_id: user?.metadata?.company_id as number,
      }),
    refetchOnWindowFocus: false,
  })

  const isGettingUserInfoLoading = isLoadingTeams || isLoadingGroups

  return (
    <PageLayout
      title="Novo Colaborador"
      description="Gerencie os dados do colaborador desta organização"
    >
      <CollaboratorForm
        isLoadingRequests={isGettingUserInfoLoading}
        allTeams={allTeams}
        allGroups={allGroups}
      />
    </PageLayout>
  )
}
