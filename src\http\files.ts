import api from '@/services/api'

export interface UploadFileResponse {
  id: number
  fileName: string
  location?: string
  name: string
  subtype: string
  type: string
  url: string
  url_thumbnail: string
}

export type UploadFileProps = {
  files: File[]
  fileTypes: string
}

export async function uploadFile(file: File[], fileTypes: string) {
  if (file.length === 0) return

  const formData = new FormData()
  formData.append('file', file[0])
  formData.append('fileTypes', fileTypes)
  const { data } = await api.post<UploadFileResponse>('/files', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })

  return data
}
