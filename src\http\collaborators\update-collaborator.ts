import { EPosition } from '@/enum/position'
import { Role } from '@/enum/role'
import { ESeniority } from '@/enum/seniority'
import { UpdateRoleUserMutationVariables } from '@/graphql/generated/graphql'
import { Avatar } from '@/model/avatar'
import api from '@/services/api'

import { updateCollaboratorRole } from './update-collaborator-role'

export interface UpdateUserProps {
  userId: number
  data: {
    name: string
    email: string
    company_id: number
    redirect_url?: string
    enrollment_id?: number
    squad_id?: number
    avatar?: Avatar
    role_ids?: number[]
    groups_ids?: number[]
    position?: EPosition
    seniority?: ESeniority
    birthdate?: string
    admitted_at?: string
  }
}

export async function editRole(userid: number, role: number) {
  const newRole: Role[] = []

  if (role === 5) {
    newRole.push(Role.B2B_MANAGER)
  } else {
    newRole.push(Role.B2B_USER)
  }

  const data: UpdateRoleUserMutationVariables = {
    role_slugs: newRole,
    user_id: Number(userid),
  }

  updateCollaboratorRole(data)
}

export async function editCollaborator({ data, userId }: UpdateUserProps) {
  const response = await api.put(`/b2b-users/${userId}`, {
    ...data,
  })

  if (data.role_ids) {
    await editRole(userId, data.role_ids[0])
  }

  return response
}
